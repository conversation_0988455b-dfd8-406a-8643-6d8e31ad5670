/* Import base styles and variables */
@import 'base.css';

/* Import component styles */
@import 'components/card.css';
@import 'components/about.css';
@import 'components/services.css';

/* Import effect styles */
@import 'effects/holographic.css';
@import 'effects/shine.css';
@import 'effects/animations.css';

/* Import responsive styles (consolidated media queries) */
@import 'responsive.css';

/* Disable main page scrollbar */
html, body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    height: 100%;
    width: 100%;
}

.card-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

/* Service Area Page Styles */
.service-area-page {
    display: none;
    opacity: 0;
    padding: 30px;
    position: absolute;
    inset: 6px;
    background-color: rgb(230, 245, 245);
    z-index: 30;
    border-radius: 8px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.05);
    overflow-y: auto;
    transition: opacity 0.4s ease;
}

/* Service area header - consistent with services header */
.service-area-header {
    max-width: 1000px;
    margin: 0 auto 20px;
    text-align: center;
}

.map-container {
    width: 100%;
    height: 450px;
    margin: 1rem 0;
    border-radius: 8px;
    overflow: hidden;
}

#service-area-map {
    width: 100%;
    height: 100%;
}

.service-area-info {
    margin: 1rem 0;
    text-align: center;
}

.service-area-description {
    font-size: 0.9rem;
    line-height: 1.4;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.service-area-footer {
    margin-top: 20px;
}

.service-area-buttons-container {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

/* Service area back button - consistent with services back button */
.service-area-back-button {
    padding: 16px 20px;
    color: white;
    text-align: center;
    font-family: var(--font-tertiary);
    font-weight: 700;
    font-size: 16px;
    letter-spacing: 1px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.25s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 5;
    min-height: 56px;
    max-width: 300px;
    background: linear-gradient(to right, var(--color-grey-dark), var(--color-grey));
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-area-back-button::before {
    content: '←';
    margin-right: 8px;
    font-size: 1.2rem;
    transition: transform 0.2s ease;
}

.service-area-back-button:hover {
    background: linear-gradient(to right, var(--color-grey-dark), var(--color-grey));
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.service-area-back-button:hover::before {
    transform: translateX(-3px);
}

.service-area-back-button:active {
    background: linear-gradient(to right, var(--color-grey), var(--color-grey-light));
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}



/* Service area main title - consistent with services title */
.service-area-main-title {
  position: relative;
  display: inline-block;
  margin-bottom: 25px;
  padding-bottom: 12px;
  color: var(--color-primary-darker);
  font-family: var(--font-tertiary);
  font-weight: 800;
  font-size: 2rem;
  letter-spacing: 0.5px;
  text-transform: none;
  text-align: center;
  width: 100%;
}

.service-area-main-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--color-primary-dark), var(--color-primary-light));
  border-radius: 4px;
  transition: width var(--transition-medium);
}

.service-area-main-title:hover::after {
  width: 120px;
}

/* Utility class for toggling page visibility */
.active-page {
  display: block !important;
}

/* Ensure all text and buttons in .service-area-page are visible */
.service-area-page * {
  z-index: 1;
}
