/**
 * @module device-detection
 * @description Provides enhanced device and browser detection capabilities
 *
 * This module offers comprehensive device detection functionality:
 * - Mobile/desktop detection based on screen size
 * - Touch capability detection
 * - Screen orientation detection (portrait/landscape)
 * - Browser identification and feature detection
 * - Device pixel ratio detection
 * - CSS class application based on device characteristics
 *
 * @example
 * import { initDeviceDetection, isMobileDevice, hasTouchCapability } from './device-detection.js';
 *
 * // Initialize device detection (adds CSS classes to HTML element)
 * const deviceDetection = initDeviceDetection();
 *
 * // Check if current device is mobile
 * if (isMobileDevice()) {
 *   // Apply mobile-specific logic
 * }
 */

/**
 * Checks if the current device is a mobile device based on screen width
 *
 * @returns {boolean} True if the device is mobile (width <= 768px), false otherwise
 *
 * @example
 * if (isMobileDevice()) {
 *   // Apply mobile-specific styles or behavior
 *   element.classList.add('mobile-view');
 * }
 */
export function isMobileDevice() {
  return window.innerWidth <= 768;
}

/**
 * Checks if the current device has touch capabilities
 *
 * @returns {boolean} True if the device has touch capabilities, false otherwise
 *
 * @example
 * if (hasTouchCapability()) {
 *   // Enable touch-specific interactions
 *   element.addEventListener('touchstart', handleTouchStart);
 * } else {
 *   // Use mouse interactions instead
 *   element.addEventListener('mousedown', handleMouseDown);
 * }
 */
export function hasTouchCapability() {
  try {
    return 'ontouchstart' in window ||
           navigator.maxTouchPoints > 0 ||
           navigator.msMaxTouchPoints > 0;
  } catch (error) {
    console.error('Error detecting touch capability:', error);
    return false;
  }
}

/**
 * Checks if the current device is in portrait orientation
 *
 * @returns {boolean} True if the device is in portrait orientation (height > width), false otherwise
 *
 * @example
 * if (isPortraitOrientation()) {
 *   // Apply portrait-specific layout
 *   container.classList.add('portrait-layout');
 * }
 */
export function isPortraitOrientation() {
  try {
    return window.innerHeight > window.innerWidth;
  } catch (error) {
    console.error('Error detecting orientation:', error);
    // Default to false if detection fails
    return false;
  }
}

/**
 * Checks if the current device is in landscape orientation
 *
 * @returns {boolean} True if the device is in landscape orientation (width > height), false otherwise
 *
 * @example
 * if (isLandscapeOrientation()) {
 *   // Apply landscape-specific layout
 *   container.classList.add('landscape-layout');
 * }
 */
export function isLandscapeOrientation() {
  try {
    return window.innerWidth > window.innerHeight;
  } catch (error) {
    console.error('Error detecting orientation:', error);
    // Default to true if detection fails (most desktop screens are landscape)
    return true;
  }
}

/**
 * Gets the device pixel ratio for high-DPI display detection
 *
 * @returns {number} The device pixel ratio (1 for standard displays, >1 for high-DPI/Retina displays)
 *
 * @example
 * const pixelRatio = getDevicePixelRatio();
 * if (pixelRatio > 1) {
 *   // Load high-resolution images
 *   image.src = '<EMAIL>';
 * }
 */
export function getDevicePixelRatio() {
  try {
    return window.devicePixelRatio || 1;
  } catch (error) {
    console.error('Error detecting device pixel ratio:', error);
    return 1; // Default to 1 if detection fails
  }
}

/**
 * Checks if the browser supports a specific CSS property
 *
 * @param {string} property - The CSS property to check (camelCase format, e.g., 'transform')
 * @returns {boolean} True if the property is supported, false otherwise
 *
 * @example
 * if (supportsCssProperty('gridTemplateColumns')) {
 *   // Use CSS Grid layout
 * } else {
 *   // Use fallback layout
 * }
 */
export function supportsCssProperty(property) {
  try {
    return property in document.documentElement.style;
  } catch (error) {
    console.error(`Error checking CSS property support for '${property}':`, error);
    return false;
  }
}

/**
 * Checks if the browser supports passive event listeners
 *
 * @returns {boolean} True if passive event listeners are supported, false otherwise
 *
 * @example
 * const options = supportsPassiveEvents() ? { passive: true } : false;
 * element.addEventListener('touchstart', handleTouchStart, options);
 */
export function supportsPassiveEvents() {
  let supportsPassive = false;
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get: function() {
        supportsPassive = true;
        return true;
      }
    });
    // Use a test event name that's unlikely to be used
    window.addEventListener('testPassiveSupport', null, opts);
    window.removeEventListener('testPassiveSupport', null, opts);
  } catch (error) {
    // Silently fail - older browsers don't support passive events
    console.debug('Passive event listeners not supported');
  }

  return supportsPassive;
}

/**
 * Gets the browser name and version from the user agent string
 *
 * @returns {Object} Object containing browser name and version
 * @property {string} name - The browser name (Chrome, Firefox, Safari, Edge, Opera, or Unknown)
 * @property {string} version - The browser version number
 *
 * @example
 * const browser = getBrowserInfo();
 * console.log(`Browser: ${browser.name} ${browser.version}`);
 *
 * // Apply browser-specific fixes
 * if (browser.name === 'Safari' && parseFloat(browser.version) < 14) {
 *   // Apply Safari-specific workaround for older versions
 * }
 */
export function getBrowserInfo() {
  try {
    const ua = navigator.userAgent;
    let browserName = "Unknown";
    let browserVersion = "Unknown";

    // Chrome
    if (/Chrome/.test(ua) && !/Chromium|Edge|Edg|OPR|Opera/.test(ua)) {
      browserName = "Chrome";
      const match = ua.match(/Chrome\/(\d+\.\d+)/);
      if (match && match[1]) {
        browserVersion = match[1];
      }
    }
    // Firefox
    else if (/Firefox/.test(ua)) {
      browserName = "Firefox";
      const match = ua.match(/Firefox\/(\d+\.\d+)/);
      if (match && match[1]) {
        browserVersion = match[1];
      }
    }
    // Safari
    else if (/Safari/.test(ua) && !/Chrome|Chromium|Edge|Edg|OPR|Opera/.test(ua)) {
      browserName = "Safari";
      const match = ua.match(/Version\/(\d+\.\d+)/);
      if (match && match[1]) {
        browserVersion = match[1];
      }
    }
    // Edge
    else if (/Edge|Edg/.test(ua)) {
      browserName = "Edge";
      const match = ua.match(/Edge\/(\d+\.\d+)|Edg\/(\d+\.\d+)/);
      if (match) {
        browserVersion = match[1] || match[2] || "Unknown";
      }
    }
    // Opera
    else if (/OPR|Opera/.test(ua)) {
      browserName = "Opera";
      const match = ua.match(/OPR\/(\d+\.\d+)|Opera\/(\d+\.\d+)/);
      if (match) {
        browserVersion = match[1] || match[2] || "Unknown";
      }
    }

    return {
      name: browserName,
      version: browserVersion
    };
  } catch (error) {
    console.error('Error detecting browser information:', error);
    return {
      name: "Unknown",
      version: "Unknown"
    };
  }
}

/**
 * Adds appropriate device classes to the HTML element for CSS targeting
 *
 * This function adds the following classes:
 * - 'is-mobile' or 'is-desktop' based on screen size
 * - 'has-touch' or 'no-touch' based on touch capability
 * - 'is-portrait' or 'is-landscape' based on orientation
 * - 'browser-[name]' based on browser detection
 *
 * @returns {void}
 *
 * @example
 * // Call once on page load
 * addDeviceClasses();
 *
 * // Call again when orientation might change
 * window.addEventListener('resize', addDeviceClasses);
 */
export function addDeviceClasses() {
  try {
    const html = document.documentElement;

    // Mobile or desktop
    if (isMobileDevice()) {
      html.classList.add('is-mobile');
      html.classList.remove('is-desktop');
    } else {
      html.classList.add('is-desktop');
      html.classList.remove('is-mobile');
    }

    // Touch capability
    if (hasTouchCapability()) {
      html.classList.add('has-touch');
      html.classList.remove('no-touch');
    } else {
      html.classList.add('no-touch');
      html.classList.remove('has-touch');
    }

    // Orientation
    if (isPortraitOrientation()) {
      html.classList.add('is-portrait');
      html.classList.remove('is-landscape');
    } else {
      html.classList.add('is-landscape');
      html.classList.remove('is-portrait');
    }

    // Browser info
    const browserInfo = getBrowserInfo();

    // Remove any existing browser classes
    Array.from(html.classList)
      .filter(cls => cls.startsWith('browser-'))
      .forEach(cls => html.classList.remove(cls));

    // Add current browser class
    html.classList.add(`browser-${browserInfo.name.toLowerCase()}`);

  } catch (error) {
    console.error('Error adding device classes:', error);
  }
}

/**
 * Initializes device detection and sets up event listeners for changes
 *
 * @returns {Object} Object containing all device detection functions for external use
 *
 * @example
 * // Initialize device detection
 * const deviceDetection = initDeviceDetection();
 *
 * // Later use the returned functions
 * if (deviceDetection.isMobileDevice()) {
 *   // Apply mobile-specific logic
 * }
 */
export function initDeviceDetection() {
  try {
    // Add initial device classes
    addDeviceClasses();

    // Create a debounced version of addDeviceClasses to avoid excessive calls
    const debouncedAddDeviceClasses = (function() {
      let timeout;
      return function() {
        clearTimeout(timeout);
        timeout = setTimeout(addDeviceClasses, 250);
      };
    })();

    // Update classes on resize (orientation change)
    window.addEventListener('resize', debouncedAddDeviceClasses);

    // Return all public functions for external use
    return {
      isMobileDevice,
      hasTouchCapability,
      isPortraitOrientation,
      isLandscapeOrientation,
      getDevicePixelRatio,
      getBrowserInfo,
      supportsCssProperty,
      supportsPassiveEvents,
      // Method to manually update device classes
      updateDeviceClasses: addDeviceClasses,
      // Cleanup method to remove event listeners
      cleanup: () => {
        window.removeEventListener('resize', debouncedAddDeviceClasses);
      }
    };
  } catch (error) {
    console.error('Error initializing device detection:', error);
    // Return empty functions that won't cause errors if called
    return {
      isMobileDevice: () => false,
      hasTouchCapability: () => false,
      isPortraitOrientation: () => false,
      isLandscapeOrientation: () => true,
      getDevicePixelRatio: () => 1,
      getBrowserInfo: () => ({ name: 'Unknown', version: 'Unknown' }),
      supportsCssProperty: () => false,
      supportsPassiveEvents: () => false,
      updateDeviceClasses: () => {},
      cleanup: () => {}
    };
  }
}
