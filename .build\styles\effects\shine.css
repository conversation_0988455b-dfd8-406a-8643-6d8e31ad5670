/**
 * Shine Effects Styles
 *
 * This file contains styles for shine and light reflection effects:
 * - Shine thick effect
 * - Shine skinny effect
 * - Shine extra thick effect
 * - Mouse gradient effect
 */

/* ======================================================
SHINE EFFECTS - LIGHT REFLECTION ANIMATIONS
====================================================== */

/**
 * Shine Effects Overview
 *
 * These elements create dynamic light reflection effects that follow mouse movement
 * across the business card. Three different shine bars with varying thickness and
 * opacity create a layered, realistic light reflection effect.
 *
 * All shine effects:
 * - Follow mouse movement horizontally across the card
 * - Rotate at 45/-45 degree angles based on mouse position
 * - Appear/disappear with smooth transitions
 * - Are clipped to the card boundaries
 */

/**
 * Primary Shine Effect - Medium Thickness
 *
 * Creates a medium-width light reflection that follows mouse movement
 * Provides the main highlight effect across the card surface
 */
.shine-thick {
  position: absolute;
  width: 120%; /* Wider than card to ensure full coverage during rotation */
  height: 30px; /* Medium thickness */
  background-color: rgba(255, 255, 255, 0.5); /* Semi-transparent white */
  transform-origin: center; /* Rotation happens from center point */
  pointer-events: none; /* Doesn't interfere with mouse interactions */
  opacity: 0; /* Hidden by default, shown on hover */
  transition: opacity var(--transition-medium), left 0.1s ease-out; /* Smooth transitions */
  z-index: 10; /* Appears above card background but below content */
  mix-blend-mode: overlay; /* Creates realistic light interaction with colors beneath */
  box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.4); /* Soft glow around the shine */
  will-change: opacity, transform, left;
}

/**
 * Secondary Shine Effect - Thin Line
 *
 * Creates a thin line of light reflection that follows mouse movement
 * Adds a subtle secondary highlight that complements the main shine
 */
.shine-skinny {
  position: absolute;
  width: 120%; /* Wider than card to ensure full coverage during rotation */
  height: 8px; /* Thin line */
  background-color: rgba(255, 255, 255, 0.5); /* Semi-transparent white */
  transform-origin: center; /* Rotation happens from center point */
  pointer-events: none; /* Doesn't interfere with mouse interactions */
  opacity: 0; /* Hidden by default, shown on hover */
  transition: opacity var(--transition-medium), left 0.1s ease-out; /* Smooth transitions */
  z-index: 10; /* Appears above card background but below content */
  mix-blend-mode: overlay; /* Creates realistic light interaction with colors beneath */
  box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.4); /* Soft glow around the shine */
}

/**
 * Tertiary Shine Effect - Diffused Glow
 *
 * Creates a wide, blurred light reflection that follows mouse movement
 * Provides a soft ambient glow effect across the entire card
 */
.shine-extra-thick {
  position: absolute;
  width: 170%; /* Much wider than card for a broad effect */
  height: 245px; /* Covers most of the card height */
  background-color: rgba(255, 255, 255, 0.3); /* More transparent than other shines */
  transform-origin: center; /* Rotation happens from center point */
  pointer-events: none; /* Doesn't interfere with mouse interactions */
  opacity: 0; /* Hidden by default, shown on hover */
  transition: opacity var(--transition-medium), left 0.1s ease-out; /* Smooth transitions */
  z-index: 10; /* Appears above card background but below content */
  mix-blend-mode: overlay; /* Creates realistic light interaction with colors beneath */
  filter: blur(30px); /* Heavy blur creates soft, diffused effect */
}

/* MOUSE GRADIENT */
.mouse-gradient {
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(circle,
    hsl(52 0% 90%) 0%,
    hsl(51 99% 50%) 20%,
    hsl(49 100% 50%) 40%,
    hsl(46 100% 50%) 60%,
    hsl(44 100% 50%) 80%);
  transform: translate(-50%, -50%) rotate(0deg);
  pointer-events: none;
  filter: blur(80px) hue-rotate(0deg) contrast(1) brightness(1);
  opacity: 0;
  transition: opacity var(--transition-medium);
  z-index: 0;
  mix-blend-mode: normal;
  will-change: opacity, transform, left, top;
}