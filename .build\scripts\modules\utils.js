/**
 * @module utils
 * @description Provides utility functions for the Mobile Massage website
 *
 * This module contains various helper functions and utilities:
 * - Math utilities for animations and effects
 * - DOM manipulation helpers
 * - Event handling utilities
 * - Safe element access and manipulation
 *
 * @example
 * import { throttle, debounce, getElement } from './utils.js';
 *
 * // Throttle a scroll event handler
 * const throttledHandler = throttle(handleScroll, 100);
 * window.addEventListener('scroll', throttledHandler);
 */

import { isMobileDevice } from './device-detection.js';

// Re-export isMobileDevice for backward compatibility
export { isMobileDevice };

/**
 * Throttles a function to limit how often it can be called
 *
 * @param {Function} func - The function to throttle
 * @param {number} limit - The time limit in milliseconds
 * @returns {Function} - Throttled function that will execute at most once per [limit] milliseconds
 *
 * @example
 * // Create a throttled mouse move handler that executes at most once every 100ms
 * const throttledMouseMove = throttle((e) => updatePosition(e.clientX, e.clientY), 100);
 * element.addEventListener('mousemove', throttledMouseMove);
 */
export function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Debounces a function to delay its execution until after a specified time
 *
 * @param {Function} func - The function to debounce
 * @param {number} delay - The delay in milliseconds
 * @returns {Function} - Debounced function that will only execute after [delay] milliseconds have passed since the last call
 *
 * @example
 * // Create a debounced resize handler that only executes 300ms after the last resize event
 * const debouncedResize = debounce(() => recalculateLayout(), 300);
 * window.addEventListener('resize', debouncedResize);
 */
export function debounce(func, delay) {
  let debounceTimer;
  return function() {
    const context = this;
    const args = arguments;
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => func.apply(context, args), delay);
  };
}

/**
 * Gets an element by ID with error handling
 *
 * @param {string} id - The ID of the element to get
 * @param {boolean} [required=false] - Whether the element is required (throws error if not found)
 * @returns {HTMLElement|null} - The element or null if not found and not required
 * @throws {Error} - If the element is required and not found
 *
 * @example
 * // Get an optional element
 * const optionalElement = getElement('optional-id');
 * if (optionalElement) {
 *   // Do something with the element
 * }
 *
 * // Get a required element
 * try {
 *   const requiredElement = getElement('required-id', true);
 *   // Element is guaranteed to exist here
 * } catch (error) {
 *   // Handle the error
 * }
 */
export function getElement(id, required = false) {
  const element = document.getElementById(id);
  if (!element) {
    const message = `Element with ID "${id}" not found`;
    if (required) {
      throw new Error(message);
    } else {
      console.warn(message);
    }
  }
  return element;
}

/**
 * Adds an event listener with error handling
 *
 * @param {HTMLElement|null|undefined} element - The element to add the listener to
 * @param {string} event - The event type (e.g., 'click', 'mousemove')
 * @param {Function} callback - The callback function to execute when the event occurs
 * @param {boolean|Object} [options=false] - Event listener options
 * @returns {boolean} - True if the listener was added successfully, false otherwise
 *
 * @example
 * // Add a click event listener safely
 * const button = document.getElementById('my-button');
 * addSafeEventListener(button, 'click', () => alert('Button clicked!'));
 */
export function addSafeEventListener(element, event, callback, options = false) {
  if (!element) {
    console.warn(`Cannot add ${event} listener to undefined element`);
    return false;
  }

  try {
    element.addEventListener(event, callback, options);
    return true;
  } catch (error) {
    console.error(`Error adding ${event} listener:`, error);
    return false;
  }
}

/**
 * Removes an event listener with error handling
 *
 * @param {HTMLElement|null|undefined} element - The element to remove the listener from
 * @param {string} event - The event type (e.g., 'click', 'mousemove')
 * @param {Function} callback - The callback function to remove
 * @param {boolean|Object} [options=false] - Event listener options
 * @returns {boolean} - True if the listener was removed successfully, false otherwise
 *
 * @example
 * // Remove a click event listener safely
 * const button = document.getElementById('my-button');
 * const clickHandler = () => alert('Button clicked!');
 * // First add the listener
 * addSafeEventListener(button, 'click', clickHandler);
 * // Later remove it
 * removeSafeEventListener(button, 'click', clickHandler);
 */
export function removeSafeEventListener(element, event, callback, options = false) {
  if (!element) {
    console.warn(`Cannot remove ${event} listener from undefined element`);
    return false;
  }

  try {
    element.removeEventListener(event, callback, options);
    return true;
  } catch (error) {
    console.error(`Error removing ${event} listener:`, error);
    return false;
  }
}

/**
 * Safely sets a style property on an element
 *
 * @param {HTMLElement|null|undefined} element - The element to style
 * @param {string} property - The CSS property (camelCase format, e.g., 'backgroundColor')
 * @param {string} value - The CSS value to set
 * @returns {boolean} - True if the style was set successfully, false otherwise
 *
 * @example
 * // Set background color safely
 * const div = document.getElementById('my-div');
 * setStyle(div, 'backgroundColor', 'red');
 *
 * // Set transform safely
 * setStyle(div, 'transform', 'translateX(10px)');
 */
export function setStyle(element, property, value) {
  if (!element || !element.style) {
    console.warn(`Cannot set style '${property}' on undefined element or element without style property`);
    return false;
  }

  try {
    element.style[property] = value;
    return true;
  } catch (error) {
    console.error(`Error setting style '${property}' to '${value}':`, error);
    return false;
  }
}

/**
 * Creates a DOM element with attributes and content
 *
 * @param {string} tag - The HTML tag name (e.g., 'div', 'span', 'button')
 * @param {Object} [attributes={}] - Key-value pairs of attributes to set on the element
 * @param {string|HTMLElement|HTMLElement[]} [content=''] - Inner content or child element(s)
 * @returns {HTMLElement} - The created element
 * @throws {Error} - If the tag name is invalid
 *
 * @example
 * // Create a button with text content
 * const button = createElement('button', {
 *   className: 'btn btn-primary',
 *   id: 'submit-btn',
 *   type: 'submit',
 *   dataset: { action: 'submit', form: 'contact' }
 * }, 'Submit');
 *
 * // Create a div with a child element
 * const childSpan = createElement('span', {}, 'Child content');
 * const parentDiv = createElement('div', { className: 'parent' }, childSpan);
 *
 * // Create a div with multiple child elements
 * const div = createElement('div', {}, [
 *   createElement('h1', {}, 'Title'),
 *   createElement('p', {}, 'Paragraph')
 * ]);
 */
export function createElement(tag, attributes = {}, content = '') {
  try {
    const element = document.createElement(tag);

    // Set attributes
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className') {
        element.className = value;
      } else if (key === 'dataset') {
        Object.entries(value).forEach(([dataKey, dataValue]) => {
          element.dataset[dataKey] = dataValue;
        });
      } else {
        element.setAttribute(key, value);
      }
    });

    // Set content
    if (content) {
      if (typeof content === 'string') {
        element.innerHTML = content;
      } else if (content instanceof HTMLElement) {
        element.appendChild(content);
      } else if (Array.isArray(content)) {
        // Handle array of elements
        content.forEach(child => {
          if (child instanceof HTMLElement) {
            element.appendChild(child);
          }
        });
      }
    }

    return element;
  } catch (error) {
    console.error(`Error creating element '${tag}':`, error);
    throw new Error(`Failed to create element '${tag}': ${error.message}`);
  }
}